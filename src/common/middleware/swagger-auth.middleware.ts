import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response, NextFunction } from 'express';
import { CustomLoggerService } from '../logger/logger.service';

@Injectable()
export class SwaggerAuthMiddleware implements NestMiddleware {
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: CustomLoggerService,
  ) {}

  use(req: Request, res: Response, next: NextFunction): void {
    // Check if Swagger protection is enabled
    const swaggerProtectionEnabled =
      this.configService.get<boolean>('swagger.protectionEnabled') ?? true;

    if (!swaggerProtectionEnabled) {
      this.logger.debug(
        'Swagger protection disabled, allowing access',
        'SwaggerAuthMiddleware',
      );
      return next();
    }

    // Get protection method from config
    const protectionMethod =
      this.configService.get<string>('swagger.protectionMethod') ?? 'api-key';

    switch (protectionMethod) {
      case 'basic':
        this.handleBasicAuth(req, res, next);
        break;
      case 'api-key':
        this.handleApiKeyAuth(req, res, next);
        break;
      case 'disabled':
        next();
        break;
      default:
        this.logger.warn(
          `Unknown Swagger protection method: ${protectionMethod}`,
          'SwaggerAuthMiddleware',
        );
        this.handleApiKeyAuth(req, res, next);
    }
  }

  private handleBasicAuth(
    req: Request,
    res: Response,
    next: NextFunction,
  ): void {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Basic ')) {
      this.logger.warn(
        'Swagger access attempt without basic auth',
        'SwaggerAuthMiddleware',
      );
      return this.sendUnauthorized(res, 'Basic');
    }

    try {
      const base64Credentials = authHeader.substring(6);
      const credentials = Buffer.from(base64Credentials, 'base64').toString(
        'ascii',
      );
      const [username, password] = credentials.split(':');

      const configuredUsername =
        this.configService.get<string>('swagger.username');
      const configuredPassword =
        this.configService.get<string>('swagger.password');

      if (!configuredUsername || !configuredPassword) {
        this.logger.error(
          'Swagger basic auth credentials not configured',
          undefined,
          'SwaggerAuthMiddleware',
        );
        return this.sendUnauthorized(res, 'Basic');
      }

      if (username === configuredUsername && password === configuredPassword) {
        this.logger.debug(
          'Swagger basic auth successful',
          'SwaggerAuthMiddleware',
        );
        return next();
      } else {
        this.logger.warn(
          'Invalid Swagger basic auth credentials',
          'SwaggerAuthMiddleware',
        );
        return this.sendUnauthorized(res, 'Basic');
      }
    } catch (error) {
      this.logger.error(
        'Error processing basic auth',
        error instanceof Error ? error.stack : String(error),
        'SwaggerAuthMiddleware',
      );
      return this.sendUnauthorized(res, 'Basic');
    }
  }

  private handleApiKeyAuth(
    req: Request,
    res: Response,
    next: NextFunction,
  ): void {
    const apiKey = this.extractApiKeyFromHeader(req);

    if (!apiKey) {
      this.logger.warn(
        'Swagger access attempt without API key',
        'SwaggerAuthMiddleware',
      );
      return this.sendApiKeyUnauthorized(res);
    }

    const configuredApiKey = this.configService.get<string>('apiKey');

    if (!configuredApiKey) {
      this.logger.error(
        'API key not configured for Swagger protection',
        undefined,
        'SwaggerAuthMiddleware',
      );
      return this.sendApiKeyUnauthorized(res);
    }

    if (apiKey === configuredApiKey) {
      this.logger.debug(
        'Swagger API key auth successful',
        'SwaggerAuthMiddleware',
      );
      return next();
    } else {
      this.logger.warn(
        'Invalid API key for Swagger access',
        'SwaggerAuthMiddleware',
      );
      return this.sendApiKeyUnauthorized(res);
    }
  }

  private extractApiKeyFromHeader(request: Request): string | undefined {
    // Check for X-API-Key header
    const xApiKey = request.headers['x-api-key'];
    if (xApiKey && typeof xApiKey === 'string') {
      return xApiKey;
    }

    // Check for Authorization header with Bearer token
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return undefined;
  }

  private sendUnauthorized(res: Response, authType: string): void {
    res.set('WWW-Authenticate', `${authType} realm="Swagger Documentation"`);
    res.status(401).json({
      statusCode: 401,
      message: 'Authentication required to access Swagger documentation',
      error: 'Unauthorized',
    });
  }

  private sendApiKeyUnauthorized(res: Response): void {
    res.status(401).json({
      statusCode: 401,
      message:
        'Valid API key required to access Swagger documentation. Provide via X-API-Key header or Authorization: Bearer token.',
      error: 'Unauthorized',
    });
  }
}
