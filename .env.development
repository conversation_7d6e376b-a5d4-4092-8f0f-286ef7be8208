# Development Environment Configuration
NODE_ENV=development
PORT=51732

# API Key Configuration
API_KEY=dev-api-key-change-in-production

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# CORS Configuration (Allow all origins in development)
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# Rate Limiting Configuration (More lenient in development)
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Logging Configuration (More verbose in development)
LOG_LEVEL=debug
LOG_FORMAT=simple

# Swagger Documentation Protection (Enabled in development for testing)
SWAGGER_PROTECTION_ENABLED=true
SWAGGER_PROTECTION_METHOD=basic
SWAGGER_USERNAME=admin
SWAGGER_PASSWORD=secure_password
